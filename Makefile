# HTTP Proxy Makefile

# Go build variables
GO_CMD = go
GO_BUILD = $(GO_CMD) build
GO_<PERSON>LEAN = $(GO_CMD) clean
GO_TEST = $(GO_CMD) test
GO_GET = $(GO_CMD) get
GO_MOD = $(GO_CMD) mod

# eBPF build variables
CLANG = clang
LLC = llc
ARCH = $(shell uname -m | sed 's/x86_64/x86/')
KERNEL_VERSION = $(shell uname -r)
KERNEL_HEADERS = /usr/src/linux-headers-$(KERNEL_VERSION)

# Directories
EBPF_DIR = ebpf
BUILD_DIR = build
CMD_DIR = cmd/httpproxy

# Targets
BINARY_NAME = httpproxy
EBPF_OBJECTS = $(BUILD_DIR)/http_filter.o

.PHONY: all build clean test deps ebpf userspace install

all: deps ebpf userspace

# Build everything
build: all

# Install Go dependencies
deps:
	$(GO_MOD) download
	$(GO_MOD) tidy

# Build eBPF programs
ebpf: $(BUILD_DIR) $(EBPF_OBJECTS)

$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)

$(BUILD_DIR)/http_filter.o: $(EBPF_DIR)/http_filter.c
	$(CLANG) -O2 -target bpf -c $< -o $@ \
		-I/usr/include/$(shell uname -m)-linux-gnu \
		-I$(KERNEL_HEADERS)/include \
		-I$(KERNEL_HEADERS)/arch/$(ARCH)/include \
		-I$(KERNEL_HEADERS)/arch/$(ARCH)/include/generated

# Build userspace Go application
userspace: $(BUILD_DIR)/$(BINARY_NAME)

$(BUILD_DIR)/$(BINARY_NAME): $(shell find . -name "*.go")
	$(GO_BUILD) -o $@ ./$(CMD_DIR)

# Run tests
test:
	$(GO_TEST) -v ./...

# Clean build artifacts
clean:
	$(GO_CLEAN)
	rm -rf $(BUILD_DIR)

# Install the binary
install: $(BUILD_DIR)/$(BINARY_NAME)
	sudo cp $< /usr/local/bin/

# Development helpers
dev-setup:
	@echo "Installing development dependencies..."
	sudo apt-get update
	sudo apt-get install -y clang llvm libbpf-dev linux-headers-$(shell uname -r)

# Run the proxy (requires sudo for eBPF)
run: $(BUILD_DIR)/$(BINARY_NAME)
	sudo $<

# Format Go code
fmt:
	$(GO_CMD) fmt ./...

# Lint Go code (requires golangci-lint)
lint:
	golangci-lint run

# Show help
help:
	@echo "Available targets:"
	@echo "  all        - Build everything (eBPF + userspace)"
	@echo "  build      - Same as 'all'"
	@echo "  deps       - Install Go dependencies"
	@echo "  ebpf       - Build eBPF programs"
	@echo "  userspace  - Build Go userspace application"
	@echo "  test       - Run tests"
	@echo "  clean      - Clean build artifacts"
	@echo "  install    - Install binary to /usr/local/bin"
	@echo "  dev-setup  - Install development dependencies"
	@echo "  run        - Build and run the proxy (requires sudo)"
	@echo "  fmt        - Format Go code"
	@echo "  lint       - Lint Go code"
	@echo "  help       - Show this help message"
