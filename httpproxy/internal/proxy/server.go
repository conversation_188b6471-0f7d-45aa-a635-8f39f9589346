package proxy

import (
	"context"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"time"

	"httpproxy/internal/ebpf"
)

// Server represents the HTTP proxy server
type Server struct {
	addr        string
	ebpfManager *ebpf.Manager
	httpServer  *http.Server
}

// NewServer creates a new proxy server instance
func NewServer(addr string, ebpfManager *ebpf.Manager) *Server {
	return &Server{
		addr:        addr,
		ebpfManager: ebpfManager,
	}
}

// Start starts the proxy server
func (s *Server) Start(ctx context.Context) error {
	mux := http.NewServeMux()
	mux.HandleFunc("/", s.handleProxy)

	s.httpServer = &http.Server{
		Addr:    s.addr,
		Handler: mux,
	}

	// Start server in goroutine
	errChan := make(chan error, 1)
	go func() {
		if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			err<PERSON>han <- err
		}
	}()

	// Wait for context cancellation or server error
	select {
	case <-ctx.Done():
		// Graceful shutdown
		shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()
		return s.httpServer.Shutdown(shutdownCtx)
	case err := <-errChan:
		return err
	}
}

// handleProxy handles HTTP proxy requests
func (s *Server) handleProxy(w http.ResponseWriter, r *http.Request) {
	fmt.Printf("Proxying request: %s %s\n", r.Method, r.URL.String())

	// Parse target URL
	targetURL, err := url.Parse(r.URL.String())
	if err != nil {
		http.Error(w, "Invalid URL", http.StatusBadRequest)
		return
	}

	// Handle CONNECT method for HTTPS tunneling
	if r.Method == http.MethodConnect {
		s.handleConnect(w, r)
		return
	}

	// Create new request to target
	proxyReq, err := http.NewRequest(r.Method, targetURL.String(), r.Body)
	if err != nil {
		http.Error(w, "Failed to create proxy request", http.StatusInternalServerError)
		return
	}

	// Copy headers
	for name, values := range r.Header {
		for _, value := range values {
			proxyReq.Header.Add(name, value)
		}
	}

	// Execute request
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(proxyReq)
	if err != nil {
		http.Error(w, "Failed to execute proxy request", http.StatusBadGateway)
		return
	}
	defer resp.Body.Close()

	// Copy response headers
	for name, values := range resp.Header {
		for _, value := range values {
			w.Header().Add(name, value)
		}
	}

	// Set status code
	w.WriteHeader(resp.StatusCode)

	// Copy response body
	io.Copy(w, resp.Body)
}

// handleConnect handles HTTPS CONNECT requests for tunneling
func (s *Server) handleConnect(w http.ResponseWriter, r *http.Request) {
	// Establish connection to target server
	targetConn, err := net.DialTimeout("tcp", r.Host, 10*time.Second)
	if err != nil {
		http.Error(w, "Failed to connect to target", http.StatusBadGateway)
		return
	}
	defer targetConn.Close()

	// Send 200 Connection Established
	w.WriteHeader(http.StatusOK)

	// Hijack the connection
	hijacker, ok := w.(http.Hijacker)
	if !ok {
		http.Error(w, "Hijacking not supported", http.StatusInternalServerError)
		return
	}

	clientConn, _, err := hijacker.Hijack()
	if err != nil {
		http.Error(w, "Failed to hijack connection", http.StatusInternalServerError)
		return
	}
	defer clientConn.Close()

	// Start bidirectional copying
	go io.Copy(targetConn, clientConn)
	io.Copy(clientConn, targetConn)
}
