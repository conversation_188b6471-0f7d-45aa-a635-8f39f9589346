package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"httpproxy/internal/proxy"
	"httpproxy/internal/ebpf"
)

func main() {
	// Initialize eBPF manager
	ebpfManager, err := ebpf.NewManager()
	if err != nil {
		log.Fatalf("Failed to initialize eBPF manager: %v", err)
	}
	defer ebpfManager.Close()

	// Load eBPF programs
	if err := ebpfManager.LoadPrograms(); err != nil {
		log.Fatalf("Failed to load eBPF programs: %v", err)
	}

	// Create HTTP proxy server
	proxyServer := proxy.NewServer(":8080", ebpfManager)

	// Setup graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle shutdown signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sig<PERSON>han, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		fmt.Println("\nShutting down proxy server...")
		cancel()
	}()

	// Start the proxy server
	fmt.Println("Starting HTTP proxy server on :8080")
	if err := proxyServer.Start(ctx); err != nil {
		log.Fatalf("Proxy server failed: %v", err)
	}
}
