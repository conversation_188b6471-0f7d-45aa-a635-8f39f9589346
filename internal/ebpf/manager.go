package ebpf

import (
	"fmt"
	"log"

	"github.com/cilium/ebpf"
	"github.com/cilium/ebpf/link"
	"github.com/cilium/ebpf/rlimit"
)

// Manager manages eBPF programs and maps
type Manager struct {
	collection *ebpf.Collection
	links      []link.Link
}

// NewManager creates a new eBPF manager
func NewManager() (*Manager, error) {
	// Remove memory limit for eBPF
	if err := rlimit.RemoveMemlock(); err != nil {
		return nil, fmt.Errorf("failed to remove memlock: %w", err)
	}

	return &Manager{
		links: make([]link.Link, 0),
	}, nil
}

// LoadPrograms loads eBPF programs from compiled objects
func (m *Manager) LoadPrograms() error {
	// TODO: Load compiled eBPF programs
	// This would typically load from a .o file generated by clang
	log.Println("eBPF programs loaded (placeholder)")
	return nil
}

// AttachPrograms attaches eBPF programs to kernel hooks
func (m *Manager) AttachPrograms() error {
	// TODO: Attach programs to appropriate hooks (TC, XDP, etc.)
	log.Println("eBPF programs attached (placeholder)")
	return nil
}

// GetMap returns a specific eBPF map by name
func (m *Manager) GetMap(name string) (*ebpf.Map, error) {
	if m.collection == nil {
		return nil, fmt.Errorf("no collection loaded")
	}

	ebpfMap, ok := m.collection.Maps[name]
	if !ok {
		return nil, fmt.Errorf("map %s not found", name)
	}

	return ebpfMap, nil
}

// UpdateMapEntry updates an entry in an eBPF map
func (m *Manager) UpdateMapEntry(mapName string, key, value interface{}) error {
	ebpfMap, err := m.GetMap(mapName)
	if err != nil {
		return err
	}

	return ebpfMap.Update(key, value, ebpf.UpdateAny)
}

// Close cleans up eBPF resources
func (m *Manager) Close() error {
	// Detach all links
	for _, l := range m.links {
		if err := l.Close(); err != nil {
			log.Printf("Failed to close link: %v", err)
		}
	}

	// Close collection
	if m.collection != nil {
		if err := m.collection.Close(); err != nil {
			log.Printf("Failed to close collection: %v", err)
		}
	}

	return nil
}
