#include <linux/bpf.h>
#include <linux/if_ether.h>
#include <linux/ip.h>
#include <linux/tcp.h>
#include <linux/in.h>
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_endian.h>

// Map to store HTTP connection information
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 10240);
    __type(key, __u32);    // Connection ID
    __type(value, __u64);  // Timestamp
} http_connections SEC(".maps");

// Map to store HTTP statistics
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 10);
    __type(key, __u32);
    __type(value, __u64);
} http_stats SEC(".maps");

// Statistics indices
#define STAT_HTTP_REQUESTS  0
#define STAT_HTTP_RESPONSES 1
#define STAT_BYTES_IN       2
#define STAT_BYTES_OUT      3

// Helper function to parse Ethernet header
static inline int parse_eth(void *data, void *data_end, struct ethhdr **eth)
{
    *eth = data;
    if ((void *)(*eth + 1) > data_end)
        return -1;
    return (*eth)->h_proto;
}

// Helper function to parse IP header
static inline int parse_ip(void *data, void *data_end, struct iphdr **ip)
{
    *ip = data;
    if ((void *)(*ip + 1) > data_end)
        return -1;
    return (*ip)->protocol;
}

// Helper function to parse TCP header
static inline int parse_tcp(void *data, void *data_end, struct tcphdr **tcp)
{
    *tcp = data;
    if ((void *)(*tcp + 1) > data_end)
        return -1;
    return 0;
}

// Check if packet contains HTTP traffic
static inline int is_http_port(__u16 port)
{
    return (port == 80 || port == 8080 || port == 3128);
}

// Update statistics
static inline void update_stats(__u32 index, __u64 value)
{
    __u64 *stat = bpf_map_lookup_elem(&http_stats, &index);
    if (stat) {
        __sync_fetch_and_add(stat, value);
    }
}

SEC("tc")
int http_filter(struct __sk_buff *skb)
{
    void *data = (void *)(long)skb->data;
    void *data_end = (void *)(long)skb->data_end;
    
    struct ethhdr *eth;
    struct iphdr *ip;
    struct tcphdr *tcp;
    
    // Parse Ethernet header
    int eth_proto = parse_eth(data, data_end, &eth);
    if (eth_proto < 0 || eth_proto != bpf_htons(ETH_P_IP))
        return TC_ACT_OK;
    
    // Parse IP header
    int ip_proto = parse_ip(data + sizeof(*eth), data_end, &ip);
    if (ip_proto < 0 || ip_proto != IPPROTO_TCP)
        return TC_ACT_OK;
    
    // Parse TCP header
    if (parse_tcp(data + sizeof(*eth) + sizeof(*ip), data_end, &tcp) < 0)
        return TC_ACT_OK;
    
    // Check if this is HTTP traffic
    __u16 src_port = bpf_ntohs(tcp->source);
    __u16 dst_port = bpf_ntohs(tcp->dest);
    
    if (!is_http_port(src_port) && !is_http_port(dst_port))
        return TC_ACT_OK;
    
    // Update statistics
    __u32 packet_size = skb->len;
    update_stats(STAT_BYTES_IN, packet_size);
    
    // Create connection ID from 4-tuple
    __u32 conn_id = ip->saddr ^ ip->daddr ^ ((__u32)src_port << 16) ^ dst_port;
    
    // Update connection tracking
    __u64 timestamp = bpf_ktime_get_ns();
    bpf_map_update_elem(&http_connections, &conn_id, &timestamp, BPF_ANY);
    
    // Log HTTP packet (for debugging)
    bpf_printk("HTTP packet: %pI4:%d -> %pI4:%d, size: %d\n", 
               &ip->saddr, src_port, &ip->daddr, dst_port, packet_size);
    
    return TC_ACT_OK;
}

char _license[] SEC("license") = "GPL";
