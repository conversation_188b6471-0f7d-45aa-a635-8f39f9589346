# HTTP Proxy with eBPF

A high-performance HTTP proxy implementation combining Go userspace components with eBPF kernel-space programs for efficient packet processing.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   HTTP Client   │───▶│   HTTP Proxy    │───▶│  Target Server  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  eBPF Programs  │
                       │  (Kernel Space) │
                       └─────────────────┘
```

### Components

- **Userspace (Go)**: HTTP proxy logic, connection handling, and eBPF management
- **Kernel Space (eBPF)**: High-performance packet filtering and statistics collection

## Features

- HTTP/HTTPS proxy support
- CONNECT method for HTTPS tunneling
- eBPF-based packet filtering and monitoring
- Real-time connection tracking
- Performance statistics collection
- Graceful shutdown handling

## Prerequisites

### System Requirements
- Linux kernel 4.18+ (for eBPF support)
- Go 1.21+
- Clang and LLVM (for eBPF compilation)
- Linux kernel headers

### Development Dependencies
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y clang llvm libbpf-dev linux-headers-$(uname -r)

# Or use the make target
make dev-setup
```

## Building

### Quick Build
```bash
make all
```

### Step-by-step Build
```bash
# Install Go dependencies
make deps

# Build eBPF programs
make ebpf

# Build userspace application
make userspace
```

## Running

The proxy requires root privileges to load eBPF programs:

```bash
# Build and run
make run

# Or run directly
sudo ./build/httpproxy
```

The proxy will start on port 8080 by default.

## Usage

### As HTTP Proxy
Configure your client to use `localhost:8080` as HTTP proxy:

```bash
# Using curl
curl -x http://localhost:8080 http://example.com

# Using environment variable
export http_proxy=http://localhost:8080
export https_proxy=http://localhost:8080
curl http://example.com
```

### Browser Configuration
Configure your browser to use `localhost:8080` as HTTP/HTTPS proxy.

## Development

### Project Structure
```
.
├── cmd/httpproxy/          # Main application entry point
├── internal/
│   ├── proxy/              # HTTP proxy implementation
│   └── ebpf/               # eBPF management
├── ebpf/                   # eBPF C programs
├── build/                  # Build artifacts
├── Makefile               # Build configuration
└── README.md
```

### Building for Development
```bash
# Format code
make fmt

# Run tests
make test

# Lint code (requires golangci-lint)
make lint
```

### eBPF Development
The eBPF programs are written in C and compiled to bytecode:

- `ebpf/http_filter.c`: Main HTTP packet filtering program
- Compiled to `build/http_filter.o`

### Adding New eBPF Programs
1. Create new `.c` file in `ebpf/` directory
2. Add compilation target to `Makefile`
3. Update Go code to load the new program

## Monitoring

The eBPF programs collect various statistics:
- HTTP request/response counts
- Bytes transferred
- Connection tracking
- Packet filtering metrics

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure running with sudo for eBPF operations
2. **eBPF Load Failed**: Check kernel version and eBPF support
3. **Compilation Errors**: Verify clang and kernel headers installation

### Debug Mode
Enable verbose logging by modifying the Go application or checking kernel logs:

```bash
# Check eBPF program logs
sudo dmesg | grep bpf

# Check kernel trace logs
sudo cat /sys/kernel/debug/tracing/trace_pipe
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes and add tests
4. Run `make test` and `make lint`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Performance Notes

- eBPF programs run in kernel space for minimal overhead
- Zero-copy packet processing where possible
- Efficient connection tracking using eBPF maps
- Optimized for high-throughput scenarios

## Future Enhancements

- [ ] Load balancing support
- [ ] SSL/TLS termination
- [ ] Advanced filtering rules
- [ ] Metrics dashboard
- [ ] Configuration file support
- [ ] Docker containerization
